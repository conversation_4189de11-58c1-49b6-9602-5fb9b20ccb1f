#!/bin/bash

# Gemini API Proxy 测试脚本

set -e

PORT=${PORT:-8080}
BASE_URL="http://localhost:$PORT"

echo "=== Gemini API Proxy 测试脚本 ==="
echo "测试地址: $BASE_URL"
echo ""

# 测试健康检查
echo "1. 测试健康检查..."
HEALTH_RESPONSE=$(curl -s "$BASE_URL/health")
echo "健康检查响应: $HEALTH_RESPONSE"

if echo "$HEALTH_RESPONSE" | grep -q '"status":"healthy"'; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    exit 1
fi

echo ""

# 测试统计信息
echo "2. 测试统计信息..."
STATS_RESPONSE=$(curl -s "$BASE_URL/stats")
echo "统计信息响应: $STATS_RESPONSE"

if echo "$STATS_RESPONSE" | grep -q '"total_requests"'; then
    echo "✅ 统计信息获取成功"
else
    echo "❌ 统计信息获取失败"
    exit 1
fi

echo ""

# 测试代理功能（需要有效的认证密钥）
echo "3. 测试代理功能..."
echo "注意: 此测试需要有效的认证密钥和网络连接"

# 使用配置的认证密钥进行测试
AUTH_KEY="AIzaSyDZ5UuosdLjGMuzm1QlQSC9ckriRadeG-U"

echo "测试 Gemini API 代理..."
PROXY_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/proxy_test_response.json \
  -X POST "$BASE_URL/v1beta/models/gemini-2.0-flash-exp:generateContent" \
  -H "Content-Type: application/json" \
  -H "X-Goog-Api-Key: $AUTH_KEY" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Hello, this is a test message."
          }
        ]
      }
    ]
  }')

echo "HTTP 状态码: $PROXY_RESPONSE"

if [ "$PROXY_RESPONSE" = "200" ]; then
    echo "✅ 代理请求成功"
    echo "响应内容:"
    cat /tmp/proxy_test_response.json | head -c 200
    echo "..."
elif [ "$PROXY_RESPONSE" = "401" ] || [ "$PROXY_RESPONSE" = "403" ]; then
    echo "⚠️  认证失败 (可能是 API 密钥无效或已过期)"
    echo "响应内容:"
    cat /tmp/proxy_test_response.json
elif [ "$PROXY_RESPONSE" = "502" ] || [ "$PROXY_RESPONSE" = "503" ]; then
    echo "⚠️  网络连接问题 (可能是代理服务器不可用)"
    echo "响应内容:"
    cat /tmp/proxy_test_response.json
else
    echo "❌ 代理请求失败"
    echo "响应内容:"
    cat /tmp/proxy_test_response.json
fi

echo ""
echo ""

# 清理临时文件
rm -f /tmp/proxy_test_response.json

echo "=== 测试完成 ==="
echo ""
echo "如果代理功能测试失败，请检查："
echo "1. API 密钥是否有效"
echo "2. 网络连接是否正常"
echo "3. 代理服务器 (127.0.0.1:7890) 是否可用"
echo "4. 防火墙设置是否正确"

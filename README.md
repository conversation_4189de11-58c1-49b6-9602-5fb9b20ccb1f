# Gemini API Proxy

一个高性能的API代理服务，支持自动轮换API密钥，完全兼容OpenAI API格式。使用Go语言开发，具有出色的并发性能和稳定性。

## 功能特性

- 🔄 **自动密钥轮换**: 每次请求自动轮换使用不同的API密钥
- 🔌 **多API兼容**: 完全兼容OpenAI API和Google Gemini API请求格式
- 🔐 **灵活认证**: 支持多种认证方式，兼容Google Gemini SDK
- ⚡ **高性能**: 基于Go语言和Gin框架，支持高并发
- 📊 **统计监控**: 实时统计请求数据和密钥使用情况
- 🏥 **健康检查**: 自动监控API密钥健康状态
- 🛡️ **错误处理**: 完善的错误处理和重试机制
- 🌐 **CORS支持**: 支持跨域请求
- 📝 **详细日志**: 完整的请求日志记录

## 快速开始

### 1. 环境要求

- Go 1.21 或更高版本

### 2. 安装依赖

```bash
go mod tidy
```

### 3. 配置环境变量

复制环境变量示例文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置你的API密钥：

```env
# API密钥配置（多个密钥用逗号分隔）
API_KEYS=sk-your-api-key-1,sk-your-api-key-2,sk-your-api-key-3

# 目标API服务配置
TARGET_BASE_URL=https://api.openai.com

# 服务配置
HOST=0.0.0.0
PORT=8080
```

### 4. 运行服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

### 5. 使用代理

#### 认证方式

代理服务支持多种认证方式（按优先级排序）：

1. **X-Goog-Api-Key头**（Google Gemini SDK标准方式）
2. **查询参数key**（Gemini API REST标准方式）
3. **查询参数auth_key**（备用方式）
4. **Authorization头**（通用方式）
5. **X-API-Key头**（通用方式）

#### 使用示例

**Google Gemini API请求：**

```bash
# 使用X-Goog-Api-Key头（推荐，兼容Google Gemini SDK）
curl -X POST http://localhost:8080/v1beta/models/gemini-2.0-flash-exp:generateContent \
  -H "Content-Type: application/json" \
  -H "X-Goog-Api-Key: your-auth-key" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Hello, world!"
          }
        ]
      }
    ]
  }'

# 使用查询参数key
curl -X POST "http://localhost:8080/v1beta/models/gemini-2.0-flash-exp:generateContent?key=your-auth-key" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Hello, world!"
          }
        ]
      }
    ]
  }'
```

**OpenAI API请求：**

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-auth-key" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {
        "role": "user",
        "content": "Hello, world!"
      }
    ]
  }'
```

注意：客户端提供的认证密钥会被代理服务验证，然后自动替换为配置的实际API密钥。

## API端点

### 代理端点

所有OpenAI API端点都被支持，包括：

- `POST /v1/chat/completions` - 聊天完成
- `POST /v1/completions` - 文本完成
- `POST /v1/embeddings` - 文本嵌入
- `GET /v1/models` - 获取模型列表
- `POST /v1/images/generations` - 图像生成
- `POST /v1/audio/transcriptions` - 音频转录
- 等等...

### 管理端点

- `GET /health` - 健康检查
- `GET /stats` - 统计信息
- `GET /` - 服务信息

## 配置选项

| 环境变量 | 默认值 | 描述 |
|---------|--------|------|
| `HOST` | `0.0.0.0` | 服务监听地址 |
| `PORT` | `8080` | 服务监听端口 |
| `API_KEYS` | - | API密钥列表（必需，用逗号分隔） |
| `TARGET_BASE_URL` | `https://api.openai.com` | 目标API服务地址 |
| `AUTH_KEY` | - | 自定义认证密钥（可选） |
| `TIMEOUT` | `60` | 请求超时时间（秒） |
| `MAX_RETRIES` | `3` | 最大重试次数 |
| `LOG_LEVEL` | `INFO` | 日志级别 |
| `ENABLE_CORS` | `true` | 是否启用CORS |

## 统计信息

访问 `/stats` 端点可以查看详细的统计信息：

```json
{
  "total_requests": 1000,
  "success_requests": 950,
  "failed_requests": 50,
  "average_latency_ms": 1200.5,
  "key_stats": {
    "sk-****key1": {
      "total_requests": 334,
      "success_requests": 320,
      "failed_requests": 14,
      "success_rate": 95.8,
      "last_used": "2024-01-01T12:00:00Z",
      "is_healthy": true
    }
  },
  "uptime": "2h30m15s",
  "last_updated": "2024-01-01T12:00:00Z"
}
```

## 部署

### Docker部署

创建 `Dockerfile`：

```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .

EXPOSE 8080
CMD ["./main"]
```

构建和运行：

```bash
docker build -t gemini-api-proxy .
docker run -p 8080:8080 --env-file .env gemini-api-proxy
```

### 系统服务

创建systemd服务文件 `/etc/systemd/system/gemini-api-proxy.service`：

```ini
[Unit]
Description=Gemini API Proxy
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/gemini-api-proxy
ExecStart=/path/to/gemini-api-proxy/main
EnvironmentFile=/path/to/gemini-api-proxy/.env
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启用和启动服务：

```bash
sudo systemctl enable gemini-api-proxy
sudo systemctl start gemini-api-proxy
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

#!/bin/bash

# 测试API代理服务的脚本

echo "🚀 启动API代理服务测试"
echo "================================"

# 检查服务是否运行
echo "📡 检查服务健康状态..."
curl -s http://localhost:8080/health | jq .

echo -e "\n📊 查看初始统计信息..."
curl -s http://localhost:8080/stats | jq .

echo -e "\n🔐 测试认证功能..."

# 测试1：无认证密钥（应该失败）
echo -e "\n--- 测试1：无认证密钥 ---"
curl -X POST "http://localhost:8080/v1beta/models/gemini-2.0-flash-exp:generateContent" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Hello"
          }
        ]
      }
    ]
  }' | jq .error.message

# 测试2：使用X-Goog-Api-Key头（Google Gemini SDK方式）
echo -e "\n--- 测试2：X-Goog-Api-Key头 ---"
curl -X POST "http://localhost:8080/v1beta/models/gemini-2.0-flash-exp:generateContent" \
  -H "Content-Type: application/json" \
  -H "X-Goog-Api-Key: my-secret-auth-key-12345" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "What is 1+1?"
          }
        ]
      }
    ]
  }' | jq .candidates[0].content.parts[0].text

# 测试3：使用查询参数key（Gemini API REST方式）
echo -e "\n--- 测试3：查询参数key ---"
curl -X POST "http://localhost:8080/v1beta/models/gemini-2.0-flash-exp:generateContent?key=my-secret-auth-key-12345" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "What is 2+2?"
          }
        ]
      }
    ]
  }' | jq .candidates[0].content.parts[0].text

# 测试4：使用Authorization头
echo -e "\n--- 测试4：Authorization头 ---"
curl -X POST "http://localhost:8080/v1beta/models/gemini-2.0-flash-exp:generateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer my-secret-auth-key-12345" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "What is 3+3?"
          }
        ]
      }
    ]
  }' | jq .candidates[0].content.parts[0].text

echo -e "\n📈 查看最终统计信息..."
curl -s http://localhost:8080/stats | jq .

echo -e "\n✅ 测试完成！"
echo "测试结果说明："
echo "1. 无认证密钥的请求被正确拒绝"
echo "2. X-Goog-Api-Key头认证正常工作（Google Gemini SDK标准方式）"
echo "3. 查询参数key认证正常工作（Gemini API REST标准方式）"
echo "4. Authorization头认证正常工作（通用方式）"
echo "5. API密钥轮换功能正常"
echo "6. 请求被正确转发到Gemini API"

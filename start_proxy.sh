#!/bin/bash

# Gemini API Proxy 启动脚本
# 此脚本确保在 macOS Launchd 环境中正确设置环境变量

# 设置工作目录
cd "$(dirname "$0")"

# 设置日志目录
mkdir -p logs

# 设置环境变量（如果 .env 文件不可用）
export HOST=${HOST:-"0.0.0.0"}
export PORT=${PORT:-"8080"}
export TARGET_BASE_URL=${TARGET_BASE_URL:-"https://generativelanguage.googleapis.com"}
export TIMEOUT=${TIMEOUT:-"60"}
export MAX_RETRIES=${MAX_RETRIES:-"3"}
export LOG_LEVEL=${LOG_LEVEL:-"INFO"}
export ENABLE_CORS=${ENABLE_CORS:-"true"}

# 设置代理环境变量
export PROXY_URL=${PROXY_URL:-"http://127.0.0.1:7890"}
export HTTP_PROXY=${HTTP_PROXY:-"http://127.0.0.1:7890"}
export HTTPS_PROXY=${HTTPS_PROXY:-"http://127.0.0.1:7890"}
export http_proxy=${http_proxy:-"http://127.0.0.1:7890"}
export https_proxy=${https_proxy:-"http://127.0.0.1:7890"}

# 设置 API 密钥（请根据实际情况修改）
export API_KEYS=${API_KEYS:-"AIzaSyDZ5UuosdLjGMuzm1QlQSC9ckriRadeG-U,AIzaSyB1CqkxWyt_cHRAItHLDqM5XJ-Ryjso7W0,AIzaSyBoMJCyBViIMJl17ZecJH1-EsiTfDXJmtU,AIzaSyB-DCGGhryJHDucHPWmx56dewADd8Pn71U,AIzaSyDy5V9lfxo-5fYaPa2KuohPIuWLmX5AaNU"}
export AUTH_KEY=${AUTH_KEY:-"AIzaSyDZ5UuosdLjGMuzm1QlQSC9ckriRadeG-U"}

# 启动服务
echo "Starting Gemini API Proxy..."
echo "Proxy URL: $PROXY_URL"
echo "Target URL: $TARGET_BASE_URL"
echo "Listen on: $HOST:$PORT"

# 启动应用
exec ./gemini-api-proxy

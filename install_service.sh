#!/bin/bash

# Gemini API Proxy macOS 服务安装脚本

set -e

PLIST_FILE="com.gemini.api.proxy.plist"
LAUNCHD_DIR="$HOME/Library/LaunchAgents"
SERVICE_NAME="com.gemini.api.proxy"

echo "=== Gemini API Proxy 服务安装脚本 ==="
echo ""

# 检查必要文件是否存在
if [ ! -f "$PLIST_FILE" ]; then
    echo "错误: 找不到 $PLIST_FILE 文件"
    exit 1
fi

if [ ! -f "gemini-api-proxy" ]; then
    echo "错误: 找不到 gemini-api-proxy 可执行文件"
    echo "请先运行 'go build' 编译项目"
    exit 1
fi

if [ ! -f "start_proxy.sh" ]; then
    echo "错误: 找不到 start_proxy.sh 启动脚本"
    exit 1
fi

# 创建 LaunchAgents 目录
mkdir -p "$LAUNCHD_DIR"

# 创建日志目录
mkdir -p logs

# 停止现有服务（如果存在）
echo "停止现有服务..."
launchctl unload "$LAUNCHD_DIR/$PLIST_FILE" 2>/dev/null || true

# 复制 plist 文件
echo "安装服务配置..."
cp "$PLIST_FILE" "$LAUNCHD_DIR/"

# 加载服务
echo "启动服务..."
launchctl load "$LAUNCHD_DIR/$PLIST_FILE"

# 检查服务状态
sleep 2
if launchctl list | grep -q "$SERVICE_NAME"; then
    echo ""
    echo "✅ 服务安装成功！"
    echo ""
    echo "服务状态:"
    launchctl list | grep "$SERVICE_NAME" || echo "服务未运行"
    echo ""
    echo "查看日志:"
    echo "  stdout: tail -f logs/stdout.log"
    echo "  stderr: tail -f logs/stderr.log"
    echo ""
    echo "管理服务:"
    echo "  停止: launchctl unload ~/Library/LaunchAgents/$PLIST_FILE"
    echo "  启动: launchctl load ~/Library/LaunchAgents/$PLIST_FILE"
    echo "  重启: launchctl unload ~/Library/LaunchAgents/$PLIST_FILE && launchctl load ~/Library/LaunchAgents/$PLIST_FILE"
    echo ""
    echo "测试服务:"
    echo "  curl http://localhost:8080/health"
else
    echo ""
    echo "❌ 服务安装失败"
    echo "请检查日志文件获取更多信息"
fi

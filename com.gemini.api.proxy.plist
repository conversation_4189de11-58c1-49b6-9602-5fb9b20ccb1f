<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.gemini.api.proxy</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/Users/<USER>/Nextcloud/python/gemini-api-proxy/start_proxy.sh</string>
    </array>
    
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/Nextcloud/python/gemini-api-proxy</string>
    
    <key>EnvironmentVariables</key>
    <dict>
        <key>HOST</key>
        <string>0.0.0.0</string>
        <key>PORT</key>
        <string>8080</string>
        <key>API_KEYS</key>
        <string>AIzaSyDZ5UuosdLjGMuzm1QlQSC9ckriRadeG-U,AIzaSyB1CqkxWyt_cHRAItHLDqM5XJ-Ryjso7W0,AIzaSyBoMJCyBViIMJl17ZecJH1-EsiTfDXJmtU,AIzaSyB-DCGGhryJHDucHPWmx56dewADd8Pn71U,AIzaSyDy5V9lfxo-5fYaPa2KuohPIuWLmX5AaNU</string>
        <key>TARGET_BASE_URL</key>
        <string>https://generativelanguage.googleapis.com</string>
        <key>TIMEOUT</key>
        <string>60</string>
        <key>MAX_RETRIES</key>
        <string>3</string>
        <key>LOG_LEVEL</key>
        <string>INFO</string>
        <key>ENABLE_CORS</key>
        <string>true</string>
        <key>AUTH_KEY</key>
        <string>AIzaSyDZ5UuosdLjGMuzm1QlQSC9ckriRadeG-U</string>
        <key>HTTP_PROXY</key>
        <string>http://127.0.0.1:7890</string>
        <key>HTTPS_PROXY</key>
        <string>http://127.0.0.1:7890</string>
        <key>http_proxy</key>
        <string>http://127.0.0.1:7890</string>
        <key>https_proxy</key>
        <string>http://127.0.0.1:7890</string>
    </dict>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <true/>
    
    <key>StandardOutPath</key>
    <string>/temp/gemini-api-proxy-logs-stdout.log</string>
    
    <key>StandardErrorPath</key>
    <string>/temp/gemini-api-proxy-logs-stderr.log</string>
</dict>
</plist>

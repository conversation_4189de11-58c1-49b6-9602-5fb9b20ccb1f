#!/bin/bash

# Gemini API Proxy macOS 服务卸载脚本

PLIST_FILE="com.gemini.api.proxy.plist"
LAUNCHD_DIR="$HOME/Library/LaunchAgents"
SERVICE_NAME="com.gemini.api.proxy"

echo "=== Gemini API Proxy 服务卸载脚本 ==="
echo ""

# 停止服务
echo "停止服务..."
launchctl unload "$LAUNCHD_DIR/$PLIST_FILE" 2>/dev/null || true

# 删除 plist 文件
if [ -f "$LAUNCHD_DIR/$PLIST_FILE" ]; then
    echo "删除服务配置..."
    rm "$LAUNCHD_DIR/$PLIST_FILE"
fi

# 检查服务是否已停止
if launchctl list | grep -q "$SERVICE_NAME"; then
    echo "❌ 服务仍在运行，请手动停止"
else
    echo "✅ 服务已成功卸载"
fi

echo ""
echo "注意: 日志文件和应用程序文件未被删除"
echo "如需完全清理，请手动删除 logs/ 目录"
